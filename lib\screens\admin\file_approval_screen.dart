import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_routes.dart';
import '../../providers/auth_provider.dart';
import '../../providers/document_provider.dart';
import '../../providers/notification_provider.dart';
import '../../providers/file_selection_provider.dart';
import '../../models/document_model.dart';
import '../../core/services/approval_service.dart';
import '../../widgets/common/app_scaffold_with_navigation.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/reusable_file_list_widget.dart';

class FileApprovalScreen extends StatefulWidget {
  const FileApprovalScreen({super.key});

  @override
  State<FileApprovalScreen> createState() => _FileApprovalScreenState();
}

class _FileApprovalScreenState extends State<FileApprovalScreen> {
  final ApprovalService _approvalService = ApprovalService();
  final TextEditingController _searchController = TextEditingController();

  String _selectedFilter = 'pending';
  String _searchQuery = '';
  bool _isLoading = false;
  List<DocumentModel> _filteredDocuments = [];
  Map<String, dynamic> _approvalStats = {};

  @override
  void initState() {
    super.initState();
    _loadApprovalData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadApprovalData() async {
    setState(() => _isLoading = true);

    try {
      // Load approval statistics
      _approvalStats = await _approvalService.getApprovalStatistics();

      // Load documents and apply filters
      await _refreshDocuments();
    } catch (e) {
      debugPrint('Error loading approval data: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _refreshDocuments() async {
    final documentProvider = Provider.of<DocumentProvider>(
      context,
      listen: false,
    );
    await documentProvider.loadAllDocumentsUnlimited();
    _applyFilters();
  }

  void _applyFilters() {
    final documentProvider = Provider.of<DocumentProvider>(
      context,
      listen: false,
    );
    final allDocuments = documentProvider.documents;

    List<DocumentModel> filtered = allDocuments;

    // Apply status filter
    switch (_selectedFilter) {
      case 'pending':
        filtered = filtered.where((doc) => doc.metadata.isPending).toList();
        break;
      case 'approved':
        filtered = filtered.where((doc) => doc.metadata.isApproved).toList();
        break;
      case 'rejected':
        filtered = filtered.where((doc) => doc.metadata.isRejected).toList();
        break;
      case 'all':
      default:
        // No additional filtering
        break;
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((doc) {
        return doc.fileName.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ) ||
            doc.uploadedBy.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }

    // Sort by upload date (newest first)
    filtered.sort((a, b) => b.uploadedAt.compareTo(a.uploadedAt));

    setState(() {
      _filteredDocuments = filtered;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<AuthProvider, DocumentProvider, FileSelectionProvider>(
      builder:
          (context, authProvider, documentProvider, selectionProvider, child) {
            return FutureBuilder<bool>(
              future: authProvider.isCurrentUserAdmin,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Scaffold(
                    body: Center(child: CircularProgressIndicator()),
                  );
                }

                if (!snapshot.hasData || !snapshot.data!) {
                  return _buildAccessDenied();
                }

                return AppScaffoldWithNavigation(
                  title: 'File Approval',
                  currentNavIndex: -1,
                  showAppBar: true,
                  actions: [
                    // Notification icon with badge
                    Consumer<NotificationProvider>(
                      builder: (context, notificationProvider, child) {
                        return Stack(
                          children: [
                            IconButton(
                              icon: const Icon(Icons.notifications_outlined),
                              onPressed: () => Navigator.pushNamed(
                                context,
                                AppRoutes.notificationCenter,
                              ),
                            ),
                            if (notificationProvider.unreadCount > 0)
                              Positioned(
                                right: 8,
                                top: 8,
                                child: Container(
                                  padding: const EdgeInsets.all(2),
                                  decoration: const BoxDecoration(
                                    color: AppColors.error,
                                    shape: BoxShape.circle,
                                  ),
                                  constraints: const BoxConstraints(
                                    minWidth: 16,
                                    minHeight: 16,
                                  ),
                                  child: Text(
                                    notificationProvider.unreadCount > 9
                                        ? '9+'
                                        : notificationProvider.unreadCount
                                              .toString(),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                          ],
                        );
                      },
                    ),
                  ],
                  body: RefreshIndicator(
                    onRefresh: _loadApprovalData,
                    child: Column(
                      children: [
                        // Statistics Card
                        if (_approvalStats.isNotEmpty) _buildStatisticsCard(),

                        // Search and Filter
                        _buildSearchAndFilter(),

                        // Bulk Actions (shown when items are selected)
                        if (selectionProvider.selectedFiles.isNotEmpty)
                          _buildBulkActions(selectionProvider),

                        // Document List
                        Expanded(child: _buildDocumentList(selectionProvider)),
                      ],
                    ),
                  ),
                );
              },
            );
          },
    );
  }

  Widget _buildDocumentList(FileSelectionProvider selectionProvider) {
    if (_isLoading) {
      return const Center(child: LoadingWidget());
    }

    if (_filteredDocuments.isEmpty) {
      return _buildEmptyState();
    }

    return ReusableFileListWidget(
      documents: _filteredDocuments,
      title: '',
      showFilter: false,
      showPagination: true,
      itemsPerPage: 25,
      emptyStateMessage: 'No documents found',
      emptyStateIcon: Icons.folder_open,
      onDocumentTap: _handleViewDetails,
      onDocumentMenu: (document) =>
          _showDocumentMenu(document, selectionProvider),
    );
  }

  Widget _buildEmptyState() {
    String message;
    String subtitle;
    IconData icon;

    switch (_selectedFilter) {
      case 'pending':
        message = 'No pending approvals';
        subtitle = 'All files have been reviewed';
        icon = Icons.check_circle_outline;
        break;
      case 'approved':
        message = 'No approved files';
        subtitle = 'Approved files will appear here';
        icon = Icons.verified_outlined;
        break;
      case 'rejected':
        message = 'No rejected files';
        subtitle = 'Rejected files will appear here';
        icon = Icons.cancel_outlined;
        break;
      default:
        message = 'No files found';
        subtitle = 'Files will appear here when uploaded';
        icon = Icons.folder_outlined;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: AppColors.textSecondary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppColors.textSecondary.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAccessDenied() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Access Denied'),
        backgroundColor: AppColors.surface,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.lock_outline, size: 64, color: AppColors.error),
            const SizedBox(height: 16),
            Text(
              'Access Denied',
              style: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'You need admin privileges to access this page',
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }

  // Action handlers
  Future<void> _handleSingleApproval(DocumentModel document) async {
    final result = await _approvalService.approveDocument(
      documentId: document.id,
    );

    if (result['success']) {
      _showSuccessMessage('Document approved successfully');
      await _refreshDocuments();

      // Send notification to user
      final notificationProvider = Provider.of<NotificationProvider>(
        context,
        listen: false,
      );
      await notificationProvider.sendFileApprovalNotification(
        userId: document.uploadedBy,
        fileName: document.fileName,
        documentId: document.id,
        approvedBy:
            Provider.of<AuthProvider>(context, listen: false).currentUser?.id ??
            '',
      );
    } else {
      _showErrorMessage(result['message']);
    }
  }

  Future<void> _handleSingleRejection(DocumentModel document) async {
    final reason = await _showRejectionDialog();
    if (reason == null) return;

    final result = await _approvalService.rejectDocument(
      documentId: document.id,
      reason: reason,
    );

    if (result['success']) {
      _showSuccessMessage('Document rejected successfully');
      await _refreshDocuments();

      // Send notification to user
      final notificationProvider = Provider.of<NotificationProvider>(
        context,
        listen: false,
      );
      await notificationProvider.sendFileRejectionNotification(
        userId: document.uploadedBy,
        fileName: document.fileName,
        documentId: document.id,
        reason: reason,
        rejectedBy:
            Provider.of<AuthProvider>(context, listen: false).currentUser?.id ??
            '',
      );
    } else {
      _showErrorMessage(result['message']);
    }
  }

  Future<void> _handleBulkApproval(
    FileSelectionProvider selectionProvider,
  ) async {
    final confirmed = await _showBulkConfirmationDialog(
      'approve',
      selectionProvider.selectedFiles.length,
    );
    if (!confirmed) return;

    final result = await _approvalService.bulkDocumentOperation(
      documentIds: selectionProvider.selectedFiles
          .map((doc) => doc.id)
          .toList(),
      operation: 'approve',
    );

    if (result['success']) {
      _showSuccessMessage('Documents approved successfully');
      selectionProvider.clearSelection();
      await _refreshDocuments();
    } else {
      _showErrorMessage(result['message']);
    }
  }

  Future<void> _handleBulkRejection(
    FileSelectionProvider selectionProvider,
  ) async {
    final reason = await _showRejectionDialog();
    if (reason == null) return;

    final confirmed = await _showBulkConfirmationDialog(
      'reject',
      selectionProvider.selectedFiles.length,
    );
    if (!confirmed) return;

    final result = await _approvalService.bulkDocumentOperation(
      documentIds: selectionProvider.selectedFiles
          .map((doc) => doc.id)
          .toList(),
      operation: 'reject',
      reason: reason,
    );

    if (result['success']) {
      _showSuccessMessage('Documents rejected successfully');
      selectionProvider.clearSelection();
      await _refreshDocuments();
    } else {
      _showErrorMessage(result['message']);
    }
  }

  Future<void> _handleBulkDeletion(
    FileSelectionProvider selectionProvider,
  ) async {
    final confirmed = await _showBulkConfirmationDialog(
      'delete',
      selectionProvider.selectedFiles.length,
    );
    if (!confirmed) return;

    final result = await _approvalService.bulkDocumentOperation(
      documentIds: selectionProvider.selectedFiles
          .map((doc) => doc.id)
          .toList(),
      operation: 'delete',
    );

    if (result['success']) {
      _showSuccessMessage('Documents deleted successfully');
      selectionProvider.clearSelection();
      await _refreshDocuments();
    } else {
      _showErrorMessage(result['message']);
    }
  }

  void _handleViewDetails(DocumentModel document) {
    Navigator.pushNamed(
      context,
      AppRoutes.documentDetails,
      arguments: {'document': document},
    );
  }

  // Dialog helpers
  Future<String?> _showRejectionDialog() async {
    final controller = TextEditingController();

    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Rejection Reason'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: 'Enter reason for rejection...',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, controller.text.trim()),
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }

  Future<bool> _showBulkConfirmationDialog(String operation, int count) async {
    final message = _approvalService.getBulkOperationConfirmationMessage(
      operation: operation,
      count: count,
    );

    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text('Confirm ${operation.toUpperCase()}'),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context, true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: operation == 'delete'
                      ? AppColors.error
                      : AppColors.primary,
                ),
                child: Text(operation.toUpperCase()),
              ),
            ],
          ),
        ) ??
        false;
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.success),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.error),
    );
  }

  // Missing widget builders
  Widget _buildStatisticsCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          _buildStatItem(
            'Pending',
            _approvalStats['pendingCount']?.toString() ?? '0',
            AppColors.warning,
          ),
          const SizedBox(width: 16),
          _buildStatItem(
            'Approved',
            _approvalStats['approvedCount']?.toString() ?? '0',
            AppColors.success,
          ),
          const SizedBox(width: 16),
          _buildStatItem(
            'Rejected',
            _approvalStats['rejectedCount']?.toString() ?? '0',
            AppColors.error,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Expanded(
      child: Column(
        children: [
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search by filename or uploader...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: AppColors.surface,
            ),
            onChanged: (query) {
              setState(() => _searchQuery = query);
              _applyFilters();
            },
          ),
          const SizedBox(height: 12),
          // Filter tabs
          Row(
            children: [
              _buildFilterTab('all', 'All'),
              _buildFilterTab('pending', 'Pending'),
              _buildFilterTab('approved', 'Approved'),
              _buildFilterTab('rejected', 'Rejected'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTab(String value, String label) {
    final isSelected = _selectedFilter == value;
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() => _selectedFilter = value);
          _applyFilters();
        },
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 2),
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primary : AppColors.surface,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected
                  ? AppColors.primary
                  : AppColors.border.withValues(alpha: 0.3),
            ),
          ),
          child: Text(
            label,
            textAlign: TextAlign.center,
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: isSelected ? Colors.white : AppColors.textSecondary,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBulkActions(FileSelectionProvider selectionProvider) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Text(
            '${selectionProvider.selectedFiles.length} selected',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.textPrimary,
            ),
          ),
          const Spacer(),
          TextButton.icon(
            onPressed: () => _handleBulkApproval(selectionProvider),
            icon: const Icon(Icons.check, size: 16),
            label: const Text('Approve'),
            style: TextButton.styleFrom(foregroundColor: AppColors.success),
          ),
          TextButton.icon(
            onPressed: () => _handleBulkRejection(selectionProvider),
            icon: const Icon(Icons.close, size: 16),
            label: const Text('Reject'),
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
          ),
          TextButton.icon(
            onPressed: () => selectionProvider.clearSelection(),
            icon: const Icon(Icons.clear, size: 16),
            label: const Text('Clear'),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  void _showDocumentMenu(
    DocumentModel document,
    FileSelectionProvider selectionProvider,
  ) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.check_circle, color: AppColors.success),
              title: const Text('Approve'),
              onTap: () {
                Navigator.pop(context);
                _handleSingleApproval(document);
              },
            ),
            ListTile(
              leading: const Icon(Icons.cancel, color: AppColors.error),
              title: const Text('Reject'),
              onTap: () {
                Navigator.pop(context);
                _handleSingleRejection(document);
              },
            ),
            ListTile(
              leading: const Icon(Icons.info_outline),
              title: const Text('View Details'),
              onTap: () {
                Navigator.pop(context);
                _handleViewDetails(document);
              },
            ),
          ],
        ),
      ),
    );
  }
}
